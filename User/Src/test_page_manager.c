/*
 * MIT License
 * Copyright (c) 2021 _VIFEXTech
 * C Language Port - Test Suite
 */
#include "page_manager.h"
#include "pm_log.h"
#include <assert.h>

/* 测试页面 */
typedef struct {
    pm_page_base_t base;
    int test_data;
} test_page_t;

/* 测试虚函数表 */
static pm_page_vtable_t test_vtable;
static bool test_load_called = false;
static bool test_appear_called = false;
static bool test_disappear_called = false;
static bool test_unload_called = false;

/* 测试页面回调函数 */
static void test_page_on_view_load(pm_page_base_t* self)
{
    test_load_called = true;
    PM_LOG_INFO("Test page load callback called");
}

static void test_page_on_view_did_appear(pm_page_base_t* self)
{
    test_appear_called = true;
    PM_LOG_INFO("Test page appear callback called");
}

static void test_page_on_view_will_disappear(pm_page_base_t* self)
{
    test_disappear_called = true;
    PM_LOG_INFO("Test page disappear callback called");
}

static void test_page_on_view_unload(pm_page_base_t* self)
{
    test_unload_called = true;
    PM_LOG_INFO("Test page unload callback called");
}

/* 测试页面工厂 */
static pm_page_base_t* test_page_factory_create(const char* name)
{
    if (strcmp(name, "TestPage") == 0) {
        test_page_t* page = (test_page_t*)malloc(sizeof(test_page_t));
        if (page != NULL) {
            memset(page, 0, sizeof(test_page_t));
            
            /* 创建基类 */
            pm_page_base_t* base_page = pm_page_base_create();
            if (base_page != NULL) {
                page->base = *base_page;
                free(base_page);  // 释放临时创建的基类
                
                /* 设置虚函数表 */
                pm_page_base_set_vtable(&page->base, &test_vtable);
                page->test_data = 12345;
                
                return &page->base;
            }
            free(page);
        }
    }
    return NULL;
}

/* 初始化测试虚函数表 */
static void init_test_vtable(void)
{
    memset(&test_vtable, 0, sizeof(pm_page_vtable_t));
    test_vtable.on_view_load = test_page_on_view_load;
    test_vtable.on_view_did_appear = test_page_on_view_did_appear;
    test_vtable.on_view_will_disappear = test_page_on_view_will_disappear;
    test_vtable.on_view_unload = test_page_on_view_unload;
}

/* 重置测试标志 */
static void reset_test_flags(void)
{
    test_load_called = false;
    test_appear_called = false;
    test_disappear_called = false;
    test_unload_called = false;
}

/* ========== 测试用例 ========== */

/* 测试页面基类创建和销毁 */
static void test_page_base_create_destroy(void)
{
    PM_LOG_INFO("Testing page base create/destroy...");
    
    pm_page_base_t* page = pm_page_base_create();
    assert(page != NULL);
    assert(pm_page_base_get_state(page) == PM_PAGE_STATE_IDLE);
    assert(pm_page_base_get_root(page) == NULL);
    
    pm_page_base_destroy(page);
    
    PM_LOG_INFO("✓ Page base create/destroy test passed");
}

/* 测试页面工厂 */
static void test_page_factory(void)
{
    PM_LOG_INFO("Testing page factory...");
    
    pm_page_factory_t* factory = pm_page_factory_create();
    assert(factory != NULL);
    
    pm_page_factory_set_create_func(factory, test_page_factory_create);
    
    pm_page_base_t* page = pm_page_factory_create_page(factory, "TestPage");
    assert(page != NULL);
    assert(strcmp(pm_page_base_get_name(page), "") == 0);  // 名称还未设置
    
    pm_page_base_destroy(page);
    pm_page_factory_destroy(factory);
    
    PM_LOG_INFO("✓ Page factory test passed");
}

/* 测试页面管理器基本功能 */
static void test_page_manager_basic(void)
{
    PM_LOG_INFO("Testing page manager basic functions...");
    
    init_test_vtable();
    
    pm_page_factory_t* factory = pm_page_factory_create();
    assert(factory != NULL);
    pm_page_factory_set_create_func(factory, test_page_factory_create);
    
    pm_page_manager_t* manager = pm_page_manager_create(factory);
    assert(manager != NULL);
    
    /* 测试页面安装 */
    bool result = pm_page_manager_install(manager, "TestPage", "TestPage");
    assert(result == true);
    
    /* 测试重复安装 */
    result = pm_page_manager_install(manager, "TestPage", "TestPage");
    assert(result == false);  // 应该失败
    
    /* 测试页面卸载 */
    result = pm_page_manager_uninstall(manager, "TestPage");
    assert(result == true);
    
    /* 测试卸载不存在的页面 */
    result = pm_page_manager_uninstall(manager, "NonExistentPage");
    assert(result == false);  // 应该失败
    
    pm_page_manager_destroy(manager);
    pm_page_factory_destroy(factory);
    
    PM_LOG_INFO("✓ Page manager basic test passed");
}

/* 测试页面路由功能 */
static void test_page_routing(void)
{
    PM_LOG_INFO("Testing page routing...");
    
    init_test_vtable();
    reset_test_flags();
    
    pm_page_factory_t* factory = pm_page_factory_create();
    assert(factory != NULL);
    pm_page_factory_set_create_func(factory, test_page_factory_create);
    
    pm_page_manager_t* manager = pm_page_manager_create(factory);
    assert(manager != NULL);
    
    /* 安装测试页面 */
    bool result = pm_page_manager_install(manager, "TestPage", "TestPage1");
    assert(result == true);
    
    result = pm_page_manager_install(manager, "TestPage", "TestPage2");
    assert(result == true);
    
    /* 测试推入页面 */
    result = pm_page_manager_push(manager, "TestPage1", NULL);
    assert(result == true);
    
    /* 验证回调函数被调用 */
    // 注意：在实际LVGL环境中，这些回调会在状态机更新时被调用
    // 这里我们只测试基本的路由逻辑
    
    /* 测试推入第二个页面 */
    result = pm_page_manager_push(manager, "TestPage2", NULL);
    assert(result == true);
    
    /* 测试弹出页面 */
    result = pm_page_manager_pop(manager);
    assert(result == true);
    
    /* 测试返回主页 */
    result = pm_page_manager_back_home(manager);
    assert(result == true);
    
    pm_page_manager_destroy(manager);
    pm_page_factory_destroy(factory);
    
    PM_LOG_INFO("✓ Page routing test passed");
}

/* 测试Stash功能 */
static void test_stash_functionality(void)
{
    PM_LOG_INFO("Testing stash functionality...");
    
    pm_page_base_t* page = pm_page_base_create();
    assert(page != NULL);
    
    /* 测试空stash */
    int test_data = 0;
    bool result = PM_STASH_POP(page, test_data);
    assert(result == false);  // 应该失败，因为stash为空
    
    pm_page_base_destroy(page);
    
    PM_LOG_INFO("✓ Stash functionality test passed");
}

/* 测试动画配置 */
static void test_animation_config(void)
{
    PM_LOG_INFO("Testing animation configuration...");
    
    pm_page_factory_t* factory = pm_page_factory_create();
    assert(factory != NULL);
    
    pm_page_manager_t* manager = pm_page_manager_create(factory);
    assert(manager != NULL);
    
    /* 测试设置全局动画 */
    pm_page_manager_set_global_load_anim_type(
        manager,
        PM_LOAD_ANIM_MOVE_LEFT,
        1000,
        lv_anim_path_linear
    );
    
    assert(pm_page_manager_get_current_load_anim_type(manager) == PM_LOAD_ANIM_MOVE_LEFT);
    
    /* 测试获取动画属性 */
    pm_load_anim_attr_t attr;
    bool result = pm_page_manager_get_load_anim_attr(manager, PM_LOAD_ANIM_OVER_LEFT, &attr);
    assert(result == true);
    assert(attr.drag_dir == PM_ROOT_DRAG_DIR_HOR);
    
    pm_page_manager_destroy(manager);
    pm_page_factory_destroy(factory);
    
    PM_LOG_INFO("✓ Animation configuration test passed");
}

/* ========== 主测试函数 ========== */

int run_page_manager_tests(void)
{
    PM_LOG_INFO("Starting Page Manager C Tests...");
    
    test_page_base_create_destroy();
    test_page_factory();
    test_page_manager_basic();
    test_page_routing();
    test_stash_functionality();
    test_animation_config();
    
    PM_LOG_INFO("✓ All Page Manager C tests passed!");
    return 0;
}

/* 简单的测试运行器 */
#ifdef TEST_STANDALONE
int main(void)
{
    return run_page_manager_tests();
}
#endif
