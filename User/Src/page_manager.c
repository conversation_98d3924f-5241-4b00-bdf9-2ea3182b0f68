/*
 * MIT License
 * Copyright (c) 2021 _VIFEXTech
 * C Language Port
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */
#include "page_manager.h"
#include "pm_log.h"

/* ========== 页面管理器实现 ========== */

/**
 * @brief  创建页面管理器
 * @param  factory: 页面工厂指针
 * @retval 页面管理器指针，失败返回NULL
 */
pm_page_manager_t *pm_page_manager_create(pm_page_factory_t *factory) {
    pm_page_manager_t *manager = (pm_page_manager_t *) malloc(sizeof(pm_page_manager_t));
    if (manager == NULL) {
        PM_LOG_ERROR("Failed to allocate memory for page manager");
        return NULL;
    }

    /* 初始化成员变量 */
    memset(manager, 0, sizeof(pm_page_manager_t));
    manager->factory = factory;
    manager->page_prev = NULL;
    manager->page_current = NULL;
    manager->root_default_style = NULL;

    /* 设置默认全局动画 */
    pm_page_manager_set_global_load_anim_type(
            manager,
            PM_LOAD_ANIM_OVER_LEFT,
            PM_ANIM_TIME_DEFAULT,
            PM_ANIM_PATH_DEFAULT
            );

    PM_LOG_INFO("Page manager created");
    return manager;
}

/**
 * @brief  销毁页面管理器
 * @param  manager: 页面管理器指针
 * @retval None
 */
void pm_page_manager_destroy(pm_page_manager_t *manager) {
    if (manager == NULL) {
        return;
    }

    /* 清空页面堆栈 */
    pm_page_manager_set_stack_clear(manager, false);

    free(manager);
    PM_LOG_INFO("Page manager destroyed");
}

/**
 * @brief  在页面池中搜索页面
 * @param  manager: 页面管理器指针
 * @param  name: 页面名称
 * @retval 页面基类指针，如果未找到则返回NULL
 */
pm_page_base_t *pm_page_manager_find_page_in_pool(const pm_page_manager_t *manager, const char *name) {
    if (manager == NULL || name == NULL) {
        return NULL;
    }

    for (size_t i = 0; i < manager->pool_size; i++) {
        if (strcmp(name, manager->page_pool[i]->name) == 0) {
            return manager->page_pool[i];
        }
    }
    return NULL;
}

/**
 * @brief  在页面堆栈中搜索页面
 * @param  manager: 页面管理器指针
 * @param  name: 页面名称
 * @retval 页面基类指针，如果未找到则返回NULL
 */
pm_page_base_t *pm_page_manager_find_page_in_stack(const pm_page_manager_t *manager, const char *name) {
    if (manager == NULL || name == NULL) {
        return NULL;
    }

    for (size_t i = 0; i < manager->stack_top; i++) {
        if (strcmp(name, manager->page_stack[i]->name) == 0) {
            return manager->page_stack[i];
        }
    }
    return NULL;
}

/**
 * @brief  获取页面堆栈的顶部页面
 * @param  manager: 页面管理器指针
 * @retval 页面基类指针，如果堆栈为空则返回NULL
 */
pm_page_base_t *pm_page_manager_get_stack_top(pm_page_manager_t *manager) {
    if (manager == NULL || manager->stack_top == 0) {
        return NULL;
    }
    return manager->page_stack[manager->stack_top - 1];
}

/**
 * @brief  获取页面堆栈顶部下方的页面
 * @param  manager: 页面管理器指针
 * @retval 页面基类指针，如果不存在则返回NULL
 */
pm_page_base_t *pm_page_manager_get_stack_top_after(const pm_page_manager_t *manager) {
    if (manager == NULL || manager->stack_top < 2) {
        return NULL;
    }
    return manager->page_stack[manager->stack_top - 2];
}

/**
 * @brief  将页面注册到页面池
 * @param  manager: 页面管理器指针
 * @param  base: 页面基类指针
 * @param  name: 页面应用名称，不允许重复注册
 * @retval 注册成功返回true
 */
bool pm_page_manager_register(pm_page_manager_t *manager, pm_page_base_t *base, const char *name) {
    if (manager == NULL || base == NULL || name == NULL) {
        PM_LOG_ERROR("Invalid parameters");
        return false;
    }

    if (manager->pool_size >= PM_MAX_PAGE_POOL_SIZE) {
        PM_LOG_ERROR("Page pool is full");
        return false;
    }

    if (pm_page_manager_find_page_in_pool(manager, name) != NULL) {
        PM_LOG_ERROR("Page(%s) was multi registered", name);
        return false;
    }

    base->manager = manager;
    strncpy(base->name, name, PM_MAX_PAGE_NAME_LEN - 1);
    base->name[PM_MAX_PAGE_NAME_LEN - 1] = '\0';

    manager->page_pool[manager->pool_size] = base;
    manager->pool_size++;

    PM_LOG_INFO("Page(%s) registered", name);
    return true;
}

/**
 * @brief  从页面池中注销页面
 * @param  manager: 页面管理器指针
 * @param  name: 页面应用名称
 * @retval 注销成功返回true
 */
bool pm_page_manager_unregister(pm_page_manager_t *manager, const char *name) {
    if (manager == NULL || name == NULL) {
        PM_LOG_ERROR("Invalid parameters");
        return false;
    }

    PM_LOG_INFO("Page(%s) unregister...", name);

    /* 检查页面是否在堆栈中 */
    pm_page_base_t *base = pm_page_manager_find_page_in_stack(manager, name);
    if (base != NULL) {
        PM_LOG_ERROR("Page(%s) was in stack", name);
        return false;
    }

    /* 在页面池中查找页面 */
    base = pm_page_manager_find_page_in_pool(manager, name);
    if (base == NULL) {
        PM_LOG_ERROR("Page(%s) was not found", name);
        return false;
    }

    /* 从页面池中移除 */
    for (size_t i = 0; i < manager->pool_size; i++) {
        if (manager->page_pool[i] == base) {
            /* 将后面的元素前移 */
            for (size_t j = i; j < manager->pool_size - 1; j++) {
                manager->page_pool[j] = manager->page_pool[j + 1];
            }
            manager->pool_size--;
            break;
        }
    }

    PM_LOG_INFO("Unregister OK");
    return true;
}

/**
 * @brief  安装页面，并将页面注册到页面池
 * @param  manager: 页面管理器指针
 * @param  class_name: 页面的类名
 * @param  app_name: 页面应用名称，不允许重复
 * @retval 成功返回true
 */
bool pm_page_manager_install(pm_page_manager_t *manager, const char *class_name, const char *app_name) {
    if (manager == NULL || class_name == NULL) {
        PM_LOG_ERROR("Invalid parameters");
        return false;
    }

    if (manager->factory == NULL) {
        PM_LOG_ERROR("Factory was not registered, can't install page");
        return false;
    }

    if (app_name == NULL) {
        PM_LOG_WARN("appName has not set");
        app_name = class_name;
    }

    if (pm_page_manager_find_page_in_pool(manager, app_name) != NULL) {
        PM_LOG_ERROR("Page(%s) was registered", app_name);
        return false;
    }

    pm_page_base_t *base = pm_page_factory_create_page(manager->factory, class_name);
    if (base == NULL) {
        PM_LOG_ERROR("Factory has not %s", class_name);
        return false;
    }

    if (!pm_page_manager_register(manager, base, app_name)) {
        PM_LOG_ERROR("Page(%s) register failed", app_name);
        return false;
    }

    PM_LOG_INFO("Page(%s) install success", app_name);
    return true;
}

/**
 * @brief  卸载页面
 * @param  manager: 页面管理器指针
 * @param  app_name: 页面应用名称
 * @retval 成功返回true
 */
bool pm_page_manager_uninstall(pm_page_manager_t *manager, const char *app_name) {
    if (manager == NULL || app_name == NULL) {
        PM_LOG_ERROR("Invalid parameters");
        return false;
    }

    pm_page_base_t *base = pm_page_manager_find_page_in_pool(manager, app_name);
    if (base == NULL) {
        PM_LOG_ERROR("Page(%s) was not found", app_name);
        return false;
    }

    if (!pm_page_manager_unregister(manager, app_name)) {
        return false;
    }

    /* 销毁页面对象 */
    pm_page_base_destroy(base);

    PM_LOG_INFO("Page(%s) uninstall success", app_name);
    return true;
}

/**
 * @brief  强制卸载页面
 * @param  manager: 页面管理器指针
 * @param  base: 页面基类指针
 * @retval 成功返回true
 */
static bool pm_page_manager_force_unload(pm_page_manager_t *manager, pm_page_base_t *base) {
    if (manager == NULL || base == NULL) {
        return false;
    }

    PM_LOG_INFO("Page(%s) force unload", base->name);

    /* 设置页面状态为卸载 */
    base->priv.state = PM_PAGE_STATE_UNLOAD;

    /* 执行状态更新 */
    pm_page_manager_state_update(manager, base);

    return true;
}

/**
 * @brief  清空页面堆栈并结束页面堆栈中所有页面的生命周期
 * @param  manager: 页面管理器指针
 * @param  keep_bottom: 是否保留堆栈底部页面
 * @retval None
 */
void pm_page_manager_set_stack_clear(pm_page_manager_t *manager, bool keep_bottom) {
    if (manager == NULL) {
        return;
    }

    while (true) {
        pm_page_base_t *top = pm_page_manager_get_stack_top(manager);

        if (top == NULL) {
            PM_LOG_INFO("Page stack is empty, breaking...");
            break;
        }

        pm_page_base_t *top_after = pm_page_manager_get_stack_top_after(manager);

        if (top_after == NULL) {
            if (keep_bottom) {
                manager->page_prev = top;
                PM_LOG_INFO("Keep page stack bottom(%s), breaking...", top->name);
                break;
            } else {
                manager->page_prev = NULL;
            }
        }

        pm_page_manager_force_unload(manager, top);

        /* 弹出堆栈 */
        manager->stack_top--;
    }
    PM_LOG_INFO("Stack clear done");
}

/**
 * @brief  获取前一页面的名称
 * @param  manager: 页面管理器指针
 * @retval 前一页面的名称，如果不存在则返回PM_EMPTY_PAGE_NAME
 */
const char *pm_page_manager_get_page_prev_name(const pm_page_manager_t *manager) {
    if (manager == NULL) {
        return PM_EMPTY_PAGE_NAME;
    }
    return manager->page_prev ? manager->page_prev->name : PM_EMPTY_PAGE_NAME;
}

/**
 * @brief  设置全局加载动画类型
 * @param  manager: 页面管理器指针
 * @param  anim: 动画类型
 * @param  time: 动画时间
 * @param  path: 动画路径
 * @retval None
 */
void pm_page_manager_set_global_load_anim_type(
        pm_page_manager_t *manager,
        pm_load_anim_t anim,
        uint16_t time,
        lv_anim_path_cb_t path) {
    if (manager == NULL) {
        return;
    }

    manager->anim_state.global.type = anim;
    manager->anim_state.global.time = time;
    manager->anim_state.global.path = path;

    /* 同步到当前动画属性 */
    manager->anim_state.current = manager->anim_state.global;

    PM_LOG_INFO("Global load anim type set to %d", anim);
}

/**
 * @brief  设置根节点默认样式
 * @param  manager: 页面管理器指针
 * @param  style: 样式指针
 * @retval None
 */
void pm_page_manager_set_root_default_style(pm_page_manager_t *manager, lv_style_t *style) {
    if (manager == NULL) {
        return;
    }
    manager->root_default_style = style;
}

/**
 * @brief  获取当前页面状态
 * @param  manager: 页面管理器指针
 * @retval 页面状态
 */
pm_page_state_t pm_page_manager_get_state(const pm_page_manager_t *manager) {
    if (manager == NULL || manager->page_current == NULL) {
        return PM_PAGE_STATE_IDLE;
    }
    return manager->page_current->priv.state;
}

/**
 * @brief  获取当前加载动画类型
 * @param  manager: 页面管理器指针
 * @retval 动画类型
 */
pm_load_anim_t pm_page_manager_get_current_load_anim_type(const pm_page_manager_t *manager) {
    if (manager == NULL) {
        return PM_LOAD_ANIM_NONE;
    }
    return (pm_load_anim_t) manager->anim_state.current.type;
}
