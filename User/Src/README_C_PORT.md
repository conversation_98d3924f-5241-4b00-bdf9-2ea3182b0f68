# 页面管理框架 - C语言版本

这是原始C++页面管理框架的C语言移植版本，保持了原有的核心功能和设计理念，同时适配了C语言的编程模式。

## 特性

- ✅ **页面生命周期管理** - 完整的页面状态机和生命周期回调
- ✅ **页面路由系统** - Push/Pop/Replace/BackHome导航功能
- ✅ **动画系统** - 多种页面切换动画效果
- ✅ **拖拽支持** - 手势拖拽返回功能
- ✅ **页面缓存** - 自动和手动缓存管理
- ✅ **工厂模式** - 动态页面创建和管理
- ✅ **Stash机制** - 页面间数据传递

## 架构对比

### C++版本 → C语言版本

| C++特性 | C语言实现 |
|---------|-----------|
| `std::vector` | 固定大小数组 + 动态管理 |
| `std::stack` | 数组实现的栈 |
| 虚函数 | 函数指针表 (vtable) |
| 类继承 | 结构体组合 |
| 多态 | 函数指针调用 |
| 构造/析构函数 | create/destroy函数 |

## 文件结构

```
page_manager_c.h        # 主头文件，包含所有数据结构和函数声明
page_manager_c.c        # 核心页面管理功能实现
pm_router_c.c          # 页面路由系统实现
pm_state_c.c           # 页面生命周期状态机实现
pm_anim_c.c            # 动画系统实现
pm_drag_c.c            # 拖拽功能实现
pm_page_base_c.c       # 页面基类实现
pm_factory_c.c         # 页面工厂实现
pm_log_c.h             # 日志系统
example_usage.c        # 使用示例
test_page_manager.c    # 测试用例
```

## 快速开始

### 1. 基本使用

```c
#include "page_manager_c.h"

// 1. 创建页面工厂
pm_page_factory_t* factory = pm_page_factory_create();
pm_page_factory_set_create_func(factory, your_page_create_function);

// 2. 创建页面管理器
pm_page_manager_t* manager = pm_page_manager_create(factory);

// 3. 安装页面
pm_page_manager_install(manager, "HomePage", "HomePage");
pm_page_manager_install(manager, "SettingsPage", "SettingsPage");

// 4. 导航到页面
pm_page_manager_push(manager, "HomePage", NULL);
pm_page_manager_push(manager, "SettingsPage", NULL);
pm_page_manager_pop(manager);
```

### 2. 创建自定义页面

```c
// 定义页面结构（继承页面基类）
typedef struct {
    pm_page_base_t base;    // 必须是第一个成员
    lv_obj_t* label;        // 页面UI元素
    int custom_data;        // 页面私有数据
} my_page_t;

// 实现页面生命周期回调
static void my_page_on_view_load(pm_page_base_t* self) {
    my_page_t* page = (my_page_t*)self;
    
    // 创建UI元素
    page->label = lv_label_create(self->root);
    lv_label_set_text(page->label, "My Page");
    lv_obj_center(page->label);
}

static void my_page_on_view_did_appear(pm_page_base_t* self) {
    // 页面显示完成后的处理
}

// 设置虚函数表
static pm_page_vtable_t my_page_vtable = {
    .on_view_load = my_page_on_view_load,
    .on_view_did_appear = my_page_on_view_did_appear,
    // ... 其他回调函数
};

// 页面工厂函数
static pm_page_base_t* create_my_page(const char* name) {
    if (strcmp(name, "MyPage") == 0) {
        my_page_t* page = malloc(sizeof(my_page_t));
        memset(page, 0, sizeof(my_page_t));
        
        // 初始化基类
        pm_page_base_t* base = pm_page_base_create();
        page->base = *base;
        free(base);
        
        // 设置虚函数表
        pm_page_base_set_vtable(&page->base, &my_page_vtable);
        
        return &page->base;
    }
    return NULL;
}
```

### 3. 页面间数据传递

```c
// 发送数据
typedef struct {
    int value;
    char message[32];
} page_data_t;

page_data_t data = {123, "Hello"};
pm_stash_t stash = PM_STASH_MAKE(data);
pm_page_manager_push(manager, "TargetPage", &stash);

// 接收数据（在目标页面的回调中）
static void target_page_on_view_load(pm_page_base_t* self) {
    page_data_t received_data;
    if (PM_STASH_POP(self, received_data)) {
        // 成功接收数据
        printf("Received: %d, %s\n", received_data.value, received_data.message);
    }
}
```

### 4. 自定义动画

```c
// 设置全局动画
pm_page_manager_set_global_load_anim_type(
    manager,
    PM_LOAD_ANIM_MOVE_LEFT,  // 动画类型
    500,                     // 动画时间(ms)
    lv_anim_path_ease_out    // 动画曲线
);

// 设置页面特定动画
pm_page_base_set_custom_load_anim_type(
    page,
    PM_LOAD_ANIM_FADE_ON,
    1000,
    lv_anim_path_linear
);
```

## 支持的动画类型

- `PM_LOAD_ANIM_OVER_LEFT` - 从左侧覆盖
- `PM_LOAD_ANIM_OVER_RIGHT` - 从右侧覆盖  
- `PM_LOAD_ANIM_OVER_TOP` - 从顶部覆盖
- `PM_LOAD_ANIM_OVER_BOTTOM` - 从底部覆盖
- `PM_LOAD_ANIM_MOVE_LEFT` - 向左推动
- `PM_LOAD_ANIM_MOVE_RIGHT` - 向右推动
- `PM_LOAD_ANIM_MOVE_TOP` - 向上推动
- `PM_LOAD_ANIM_MOVE_BOTTOM` - 向下推动
- `PM_LOAD_ANIM_FADE_ON` - 淡入淡出
- `PM_LOAD_ANIM_NONE` - 无动画

## 页面生命周期

```
IDLE → LOAD → WILL_APPEAR → DID_APPEAR → ACTIVITY
                                           ↓
IDLE ← UNLOAD ← DID_DISAPPEAR ← WILL_DISAPPEAR
```

每个状态都有对应的回调函数：
- `on_view_load` - 页面加载时调用
- `on_view_will_appear` - 页面即将显示时调用
- `on_view_did_appear` - 页面显示完成后调用
- `on_view_will_disappear` - 页面即将消失时调用
- `on_view_did_disappear` - 页面消失完成后调用
- `on_view_unload` - 页面卸载时调用

## 配置选项

在 `page_manager_c.h` 中可以配置：

```c
#define PM_MAX_PAGE_POOL_SIZE    32    // 最大页面池大小
#define PM_MAX_PAGE_STACK_SIZE   16    // 最大页面堆栈大小
#define PM_MAX_PAGE_NAME_LEN     32    // 最大页面名称长度
```

## 编译和测试

```bash
# 编译测试（需要LVGL环境）
gcc -o test test_page_manager.c page_manager_c.c pm_*.c -llvgl

# 运行测试
./test
```

## 注意事项

1. **内存管理**：C版本需要手动管理内存，确保正确调用create/destroy函数
2. **LVGL依赖**：框架依赖LVGL图形库，确保正确链接
3. **线程安全**：框架不是线程安全的，在多线程环境中需要额外同步
4. **错误处理**：检查函数返回值，处理可能的错误情况

## 与C++版本的兼容性

C语言版本在功能上与C++版本保持一致，主要差异：
- 使用函数指针替代虚函数
- 使用结构体替代类
- 手动内存管理替代RAII
- 固定大小数组替代STL容器

## 许可证

MIT License - 与原始C++版本相同
