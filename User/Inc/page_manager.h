/*
 * MIT License
 * Copyright (c) 2021 _VIFEXTech
 * C Language Port
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */
#ifndef __PAGE_MANAGER_C_H
#define __PAGE_MANAGER_C_H

#include "lvgl.h"
#include <stdint.h>
#include <stdbool.h>
#include <string.h>
#include <stdlib.h>

#ifdef __cplusplus
extern "C" {
#endif

/* 页面管理器配置 */
#define PM_MAX_PAGE_POOL_SIZE    32    // 最大页面池大小
#define PM_MAX_PAGE_STACK_SIZE   16    // 最大页面堆栈大小
#define PM_MAX_PAGE_NAME_LEN     32    // 最大页面名称长度

#define PM_EMPTY_PAGE_NAME       "EMPTY"

/* 页面动画默认参数 */
#define PM_ANIM_TIME_DEFAULT     500   // 默认动画时间(ms)
#define PM_ANIM_PATH_DEFAULT     lv_anim_path_ease_out

/* 生成stash区域数据 */
#define PM_STASH_MAKE(data) {&(data), sizeof(data)}

/* 从stash区域获取数据 */
#define PM_STASH_POP(page, data) pm_page_stash_pop(page, &(data), sizeof(data))

/* 前向声明 */
typedef struct pm_page_manager pm_page_manager_t;
typedef struct pm_page_base pm_page_base_t;
typedef struct pm_page_factory pm_page_factory_t;

/* 页面状态枚举 */
typedef enum {
    PM_PAGE_STATE_IDLE,
    PM_PAGE_STATE_LOAD,
    PM_PAGE_STATE_WILL_APPEAR,
    PM_PAGE_STATE_DID_APPEAR,
    PM_PAGE_STATE_ACTIVITY,
    PM_PAGE_STATE_WILL_DISAPPEAR,
    PM_PAGE_STATE_DID_DISAPPEAR,
    PM_PAGE_STATE_UNLOAD,
    _PM_PAGE_STATE_LAST
} pm_page_state_t;

/* 页面切换动画类型 */
typedef enum {
    /* 默认（全局）动画类型 */
    PM_LOAD_ANIM_GLOBAL = 0,

    /* 新页面覆盖旧页面 */
    PM_LOAD_ANIM_OVER_LEFT,        // 从左侧覆盖
    PM_LOAD_ANIM_OVER_RIGHT,       // 从右侧覆盖
    PM_LOAD_ANIM_OVER_TOP,         // 从顶部覆盖
    PM_LOAD_ANIM_OVER_BOTTOM,      // 从底部覆盖

    /* 新页面推动旧页面 */
    PM_LOAD_ANIM_MOVE_LEFT,        // 向左推动
    PM_LOAD_ANIM_MOVE_RIGHT,       // 向右推动
    PM_LOAD_ANIM_MOVE_TOP,         // 向上推动
    PM_LOAD_ANIM_MOVE_BOTTOM,      // 向下推动

    /* 新界面淡入，旧页面淡出 */
    PM_LOAD_ANIM_FADE_ON,

    /* 无动画 */
    PM_LOAD_ANIM_NONE,

    _PM_LOAD_ANIM_LAST = PM_LOAD_ANIM_NONE
} pm_load_anim_t;

/* 页面拖拽方向 */
typedef enum {
    PM_ROOT_DRAG_DIR_NONE,         // 无拖拽方向
    PM_ROOT_DRAG_DIR_HOR,          // 水平拖拽
    PM_ROOT_DRAG_DIR_VER,          // 垂直拖拽
} pm_root_drag_dir_t;

/* Stash数据区域 */
typedef struct {
    void* ptr;
    uint32_t size;
} pm_stash_t;

/* 页面切换动画属性 */
typedef struct {
    uint8_t type;
    uint16_t time;
    lv_anim_path_cb_t path;
} pm_anim_attr_t;

/* 动画设置器和获取器函数指针 */
typedef void(*pm_anim_setter_t)(void*, int32_t);
typedef int32_t(*pm_anim_getter_t)(void*);

/* 动画值结构 */
typedef struct {
    /* 作为进入方 */
    struct {
        int32_t start;              // 动画起始值
        int32_t end;                // 动画结束值
    } enter;

    /* 作为退出方 */
    struct {
        int32_t start;              // 动画起始值
        int32_t end;                // 动画结束值
    } exit;
} pm_anim_value_t;

/* 页面加载动画属性 */
typedef struct {
    pm_anim_setter_t setter;        // 动画设置器函数
    pm_anim_getter_t getter;        // 动画获取器函数
    pm_root_drag_dir_t drag_dir;    // 拖拽方向
    pm_anim_value_t push;           // 推入动画值
    pm_anim_value_t pop;            // 弹出动画值
} pm_load_anim_attr_t;

/* 页面基类虚函数表 */
typedef struct {
    /* 页面生命周期回调函数 */
    void (*on_custom_attr_config)(pm_page_base_t* self);
    void (*on_view_load)(pm_page_base_t* self);
    void (*on_view_did_load)(pm_page_base_t* self);
    void (*on_view_will_appear)(pm_page_base_t* self);
    void (*on_view_did_appear)(pm_page_base_t* self);
    void (*on_view_will_disappear)(pm_page_base_t* self);
    void (*on_view_did_disappear)(pm_page_base_t* self);
    void (*on_view_unload)(pm_page_base_t* self);
    void (*on_view_did_unload)(pm_page_base_t* self);
} pm_page_vtable_t;

/* 页面基类结构 */
struct pm_page_base {
    /* 公共成员 */
    lv_obj_t* root;                 // UI根节点
    pm_page_manager_t* manager;     // 页面管理器指针
    char name[PM_MAX_PAGE_NAME_LEN]; // 页面名称
    uint16_t id;                    // 页面ID
    void* user_data;                // 用户数据指针

    /* 虚函数表 */
    pm_page_vtable_t* vtable;

    /* 私有数据，仅页面管理器访问 */
    struct {
        bool req_enable_cache;        // 缓存启用请求
        bool req_disable_auto_cache;  // 自动缓存管理启用请求

        bool is_disable_auto_cache;   // 是否为自动缓存管理
        bool is_cached;               // 缓存启用

        pm_stash_t stash;             // Stash区域
        pm_page_state_t state;        // 页面状态

        /* 动画状态 */
        struct {
            bool is_enter;            // 是否为进入方
            bool is_busy;             // 动画是否正在播放
            pm_anim_attr_t attr;      // 动画属性
        } anim;
    } priv;
};

/* 页面工厂结构 */
struct pm_page_factory {
    /* 创建页面的函数指针 */
    pm_page_base_t* (*create_page)(const char* name);
};

/* 页面管理器结构 */
struct pm_page_manager {
    /* 页面工厂 */
    pm_page_factory_t* factory;

    /* 页面池 */
    pm_page_base_t* page_pool[PM_MAX_PAGE_POOL_SIZE];
    size_t pool_size;

    /* 页面堆栈 */
    pm_page_base_t* page_stack[PM_MAX_PAGE_STACK_SIZE];
    size_t stack_top;

    /* 前一页面 */
    pm_page_base_t* page_prev;

    /* 当前页面 */
    pm_page_base_t* page_current;

    /* 页面动画状态 */
    struct {
        bool is_switch_req;           // 是否有切换请求
        bool is_busy;                 // 是否正在切换
        bool is_entering;             // 是否正在执行进入动作

        pm_anim_attr_t current;       // 当前动画属性
        pm_anim_attr_t global;        // 全局动画属性
    } anim_state;

    /* 根节点样式 */
    lv_style_t* root_default_style;
};

/* ========== 页面管理器函数声明 ========== */

/* 页面管理器生命周期 */
pm_page_manager_t* pm_page_manager_create(pm_page_factory_t* factory);
void pm_page_manager_destroy(pm_page_manager_t* manager);

/* 页面加载器 */
bool pm_page_manager_install(pm_page_manager_t* manager, const char* class_name, const char* app_name);
bool pm_page_manager_uninstall(pm_page_manager_t* manager, const char* app_name);
bool pm_page_manager_register(pm_page_manager_t* manager, pm_page_base_t* base, const char* name);
bool pm_page_manager_unregister(pm_page_manager_t* manager, const char* name);

/* 页面路由 */
bool pm_page_manager_replace(pm_page_manager_t* manager, const char* name, const pm_stash_t* stash);
bool pm_page_manager_push(pm_page_manager_t* manager, const char* name, const pm_stash_t* stash);
bool pm_page_manager_pop(pm_page_manager_t* manager);
bool pm_page_manager_back_home(pm_page_manager_t* manager);
const char* pm_page_manager_get_page_prev_name(const pm_page_manager_t* manager);

/* 全局动画 */
void pm_page_manager_set_global_load_anim_type(
    pm_page_manager_t* manager,
    pm_load_anim_t anim,
    uint16_t time,
    lv_anim_path_cb_t path
);
void pm_page_manager_set_root_default_style(pm_page_manager_t* manager, lv_style_t* style);

/* 获取状态 */
pm_page_state_t pm_page_manager_get_state(const pm_page_manager_t* manager);
pm_load_anim_t pm_page_manager_get_current_load_anim_type(const pm_page_manager_t* manager);

/* ========== 页面基类函数声明 ========== */

/* 页面基类生命周期 */
pm_page_base_t* pm_page_base_create(void);
void pm_page_base_destroy(pm_page_base_t* page);

/* 页面配置 */
void pm_page_base_set_custom_cache_enable(pm_page_base_t* page, bool en);
void pm_page_base_set_custom_auto_cache_enable(pm_page_base_t* page, bool en);
void pm_page_base_set_custom_load_anim_type(
    pm_page_base_t* page,
    uint8_t anim_type,
    uint16_t time,
    lv_anim_path_cb_t path
);

/* Stash操作 */
bool pm_page_stash_pop(pm_page_base_t* page, void* ptr, uint32_t size);

/* 页面基类扩展功能 */
void pm_page_base_set_vtable(pm_page_base_t* page, pm_page_vtable_t* vtable);
pm_page_vtable_t* pm_page_base_get_vtable(pm_page_base_t* page);
void pm_page_base_set_user_data(pm_page_base_t* page, void* user_data);
void* pm_page_base_get_user_data(pm_page_base_t* page);
pm_page_state_t pm_page_base_get_state(pm_page_base_t* page);
const char* pm_page_base_get_name(pm_page_base_t* page);
lv_obj_t* pm_page_base_get_root(pm_page_base_t* page);
pm_page_manager_t* pm_page_base_get_manager(pm_page_base_t* page);

/* ========== 页面工厂函数声明 ========== */

/* 页面工厂生命周期 */
pm_page_factory_t* pm_page_factory_create(void);
void pm_page_factory_destroy(pm_page_factory_t* factory);

/* 设置创建函数 */
void pm_page_factory_set_create_func(pm_page_factory_t* factory,
    pm_page_base_t* (*create_func)(const char* name));

/* 创建页面 */
pm_page_base_t* pm_page_factory_create_page(pm_page_factory_t* factory, const char* name);

/* ========== 内部函数声明（用于模块间调用） ========== */

/* 页面池和堆栈管理 */
pm_page_base_t* pm_page_manager_find_page_in_pool(const pm_page_manager_t* manager, const char* name);
pm_page_base_t* pm_page_manager_find_page_in_stack(const pm_page_manager_t* manager, const char* name);
pm_page_base_t* pm_page_manager_get_stack_top(pm_page_manager_t* manager);
pm_page_base_t* pm_page_manager_get_stack_top_after(const pm_page_manager_t* manager);
void pm_page_manager_set_stack_clear(pm_page_manager_t* manager, bool keep_bottom);

/* 页面状态管理 */
void pm_page_manager_state_update(pm_page_manager_t* manager, pm_page_base_t* page);
pm_page_state_t pm_page_manager_state_load_execute(pm_page_manager_t* manager, pm_page_base_t* page);
pm_page_state_t pm_page_manager_state_will_appear_execute(pm_page_manager_t* manager, pm_page_base_t* page);
pm_page_state_t pm_page_manager_state_did_appear_execute(pm_page_manager_t* manager, pm_page_base_t* page);
pm_page_state_t pm_page_manager_state_will_disappear_execute(pm_page_manager_t* manager, pm_page_base_t* page);
pm_page_state_t pm_page_manager_state_did_disappear_execute(pm_page_manager_t* manager, pm_page_base_t* page);
pm_page_state_t pm_page_manager_state_unload_execute(pm_page_manager_t* manager, pm_page_base_t* page);

/* 动画管理 */
void pm_page_manager_switch_anim_type_update(pm_page_manager_t* manager, pm_page_base_t* page);
void pm_page_manager_switch_anim_create(pm_page_manager_t* manager, pm_page_base_t* page);
bool pm_page_manager_get_load_anim_attr(pm_page_manager_t* manager, uint8_t anim, pm_load_anim_attr_t* attr);
bool pm_page_manager_get_current_load_anim_attr(pm_page_manager_t* manager, pm_load_anim_attr_t* attr);
void pm_page_manager_anim_default_init(pm_page_manager_t* manager, lv_anim_t* a);

/* 拖拽管理 */
void pm_page_manager_root_enable_drag(pm_page_manager_t* manager, lv_obj_t* root);

#ifdef __cplusplus
}
#endif

#endif /* __PAGE_MANAGER_C_H */
